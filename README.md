# 🎯 Reddit User Persona Generator

A comprehensive tool that analyzes Reddit users' posting behavior and generates detailed user personas with professional visualizations, similar to UX research personas.

## ✨ Features

- **🔍 Deep Reddit Analysis**: Scrapes posts, comments, and behavioral patterns
- **🤖 AI-Powered Insights**: Uses Google Gemini AI for intelligent persona generation
- **📊 Data Visualizations**: Creates charts for subreddit activity, posting times, and word clouds
- **🎨 Professional Persona Cards**: Generates beautiful HTML persona cards like the Lucas Mellor example
- **📋 Comprehensive Reports**: Detailed text reports with citations and data analysis
- **📈 Behavioral Analytics**: Sentiment analysis, writing style assessment, and activity patterns

## 🚀 Quick Start

### 1. Setup
```bash
# Clone or download the project
git clone <your-repo-url>
cd reddit-persona-generator

# Run setup script
python setup.py
```

### 2. Configure API Keys
Edit the `.env` file with your credentials:

```env
# Reddit API (https://www.reddit.com/prefs/apps)
REDDIT_CLIENT_ID=your_client_id_here
REDDIT_CLIENT_SECRET=your_client_secret_here
REDDIT_USER_AGENT=PersonaGenerator/1.0 by YourUsername

# Google Gemini API (https://makersuite.google.com/app/apikey)
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Run Analysis
```bash
python personal.py
```

Enter a Reddit profile URL when prompted:
```
https://www.reddit.com/user/username/
```

## 📁 Output Files

The tool generates multiple files for comprehensive analysis:

```
output/
├── username_persona.txt          # Detailed text persona with citations
├── username_persona.html         # Beautiful visual persona card
├── username_raw_data.json        # Raw scraped data for further analysis
└── username_analysis/            # Data visualizations
    ├── subreddit_activity.png     # Top subreddits chart
    ├── posting_times.png          # Activity heatmap by hour
    └── wordcloud.png              # Most common words
```

## 🎨 Persona Card Features

The generated HTML persona cards include:

- **Basic Demographics**: Age, occupation, location estimates
- **Behavior & Habits**: Specific patterns with citations
- **Frustrations**: Pain points identified from negative sentiment
- **Motivations**: Visual progress bars for key drivers
- **Personality Traits**: MBTI-style personality scales
- **Goals & Needs**: Primary objectives and desires

## 🔧 API Setup Instructions

### Reddit API Setup
1. Go to https://www.reddit.com/prefs/apps
2. Click "Create App" or "Create Another App"
3. Choose "script" as the app type
4. Note down your `client_id` and `client_secret`

### Google Gemini API Setup
1. Visit https://makersuite.google.com/app/apikey
2. Create a new API key
3. Copy the key to your `.env` file

## 📊 Analysis Capabilities

### Behavioral Analysis
- **Posting Patterns**: Time-based activity analysis
- **Subreddit Preferences**: Community engagement patterns
- **Sentiment Analysis**: Emotional tone of communications
- **Writing Style**: Complexity and readability metrics

### Data Visualizations
- **Subreddit Activity Chart**: Horizontal bar chart of most active communities
- **Posting Time Heatmap**: 24-hour activity pattern visualization
- **Word Cloud**: Most frequently used terms (excluding common words)

### AI-Generated Insights
- **Demographic Estimation**: Age, occupation, location inference
- **Personality Assessment**: MBTI-style trait analysis
- **Motivation Mapping**: Key drivers and preferences
- **Goal Identification**: Primary needs and objectives

## 🛠️ Dependencies

- `praw`: Reddit API wrapper
- `google-generativeai`: Google Gemini AI integration
- `matplotlib`: Chart generation
- `seaborn`: Statistical visualizations
- `wordcloud`: Word cloud generation
- `nltk`: Natural language processing
- `textstat`: Text readability analysis
- `pandas`: Data manipulation

## 📝 Example Usage

```python
# Basic usage
python personal.py

# The script will prompt for:
# Paste Reddit profile URL: https://www.reddit.com/user/example_user/

# Output:
# 🔍 Analyzing user: example_user
# 📝 Scraping posts...
# 💬 Scraping comments...
# ✅ Data collection complete!
# 🤖 Generating professional persona...
# 📊 Creating data visualizations...
# 🎨 Creating visual persona card...
# 🎉 Analysis complete!
```

## 🔒 Privacy & Ethics

- **Public Data Only**: Only analyzes publicly available Reddit posts and comments
- **No Personal Information**: Does not collect or store personal identifying information
- **Research Purpose**: Intended for UX research, marketing analysis, and academic study
- **Respect Privacy**: Always respect user privacy and Reddit's terms of service

## 🤝 Contributing

Contributions are welcome! Areas for improvement:

- Enhanced AI prompts for better persona accuracy
- Additional visualization types
- Support for other social media platforms
- Improved HTML template designs
- Better sentiment analysis models

## 📄 License

This project is open source. Please use responsibly and in accordance with Reddit's API terms of service.

## 🆘 Troubleshooting

### Common Issues

**"Invalid Reddit profile URL"**
- Ensure URL format: `https://www.reddit.com/user/username/`
- Check that the username exists and is public

**"Error scraping user data"**
- Verify Reddit API credentials in `.env`
- Check if the user has public posts/comments
- Ensure internet connection is stable

**"Error generating persona"**
- Verify Gemini API key in `.env`
- Check if you have sufficient API quota
- Try with a user who has more activity

**Missing visualizations**
- Install all dependencies: `pip install -r requirements.txt`
- Check if matplotlib backend is properly configured
- Ensure sufficient disk space in output directory

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Verify all API credentials are correct
3. Ensure all dependencies are installed
4. Check that the user profile is public and has recent activity

---

**⚠️ Disclaimer**: This tool is for research and analysis purposes. Always respect user privacy and platform terms of service.
