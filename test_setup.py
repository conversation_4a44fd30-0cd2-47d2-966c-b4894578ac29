#!/usr/bin/env python3
"""
Test script to verify Reddit Persona Generator setup
"""

import os
import sys
from dotenv import load_dotenv

def test_imports():
    """Test if all required packages can be imported"""
    print("🧪 Testing package imports...")
    
    required_packages = [
        ('praw', 'Reddit API wrapper'),
        ('google.generativeai', 'Google Gemini AI'),
        ('matplotlib.pyplot', 'Plotting library'),
        ('seaborn', 'Statistical visualization'),
        ('wordcloud', 'Word cloud generation'),
        ('pandas', 'Data manipulation'),
        ('textstat', 'Text analysis'),
        ('nltk', 'Natural language processing')
    ]
    
    failed_imports = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError as e:
            print(f"  ❌ {package} - {description} (Error: {e})")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All packages imported successfully!")
        return True

def test_env_file():
    """Test if .env file exists and has required variables"""
    print("\n🔧 Testing environment configuration...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("💡 Copy .env.example to .env and add your API keys")
        return False
    
    load_dotenv()
    
    required_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET', 
        'REDDIT_USER_AGENT',
        'GEMINI_API_KEY'
    ]
    
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == f'your_{var.lower()}_here':
            print(f"  ❌ {var} - Not configured")
            missing_vars.append(var)
        else:
            print(f"  ✅ {var} - Configured")
    
    if missing_vars:
        print(f"\n❌ Missing configuration: {', '.join(missing_vars)}")
        print("💡 Edit .env file with your actual API credentials")
        return False
    else:
        print("✅ All environment variables configured!")
        return True

def test_reddit_connection():
    """Test Reddit API connection"""
    print("\n🔗 Testing Reddit API connection...")
    
    try:
        import praw
        load_dotenv()
        
        reddit = praw.Reddit(
            client_id=os.getenv("REDDIT_CLIENT_ID"),
            client_secret=os.getenv("REDDIT_CLIENT_SECRET"),
            user_agent=os.getenv("REDDIT_USER_AGENT"),
        )
        
        # Test with a simple request
        subreddit = reddit.subreddit("test")
        subreddit.display_name  # This will trigger an API call
        
        print("✅ Reddit API connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Reddit API connection failed: {e}")
        print("💡 Check your Reddit API credentials in .env file")
        return False

def test_gemini_connection():
    """Test Google Gemini API connection"""
    print("\n🤖 Testing Google Gemini API connection...")
    
    try:
        import google.generativeai as genai
        load_dotenv()
        
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Test with a simple request
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Hello, this is a test.")
        
        if response.text:
            print("✅ Google Gemini API connection successful!")
            return True
        else:
            print("❌ Google Gemini API returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Google Gemini API connection failed: {e}")
        print("💡 Check your Gemini API key in .env file")
        return False

def test_output_directory():
    """Test if output directory can be created"""
    print("\n📁 Testing output directory...")
    
    try:
        os.makedirs("output", exist_ok=True)
        
        # Test write permissions
        test_file = "output/test_write.txt"
        with open(test_file, "w") as f:
            f.write("test")
        
        os.remove(test_file)
        print("✅ Output directory is writable!")
        return True
        
    except Exception as e:
        print(f"❌ Output directory test failed: {e}")
        return False

def main():
    print("🧪 Reddit User Persona Generator - Setup Test")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_env_file,
        test_output_directory,
        test_reddit_connection,
        test_gemini_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("💡 You can now run: python personal.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("💡 Check the README.md for detailed setup instructions.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
