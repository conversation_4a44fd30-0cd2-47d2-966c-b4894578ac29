<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hungry-Move-6603 - Dynamic User Persona</title>
    <style>
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B6B22, #4ECDC422);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .persona-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-areas:
                "header header"
                "content insights"
                "footer footer";
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto 1fr auto;
            min-height: 90vh;
        }

        .persona-header {
            grid-area: header;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 40px;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .user-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            border: 4px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }

        .user-avatar[data-style="gaming"] {
            background: linear-gradient(45deg, #9B59B6, #3498DB);
            box-shadow: 0 0 30px rgba(155, 89, 182, 0.5);
        }

        .user-avatar[data-style="creative"] {
            background: linear-gradient(45deg, #E74C3C, #F39C12);
            box-shadow: 0 0 30px rgba(231, 76, 60, 0.5);
        }

        .user-avatar[data-style="tech"] {
            background: linear-gradient(45deg, #2ECC71, #1ABC9C);
            box-shadow: 0 0 30px rgba(46, 204, 113, 0.5);
        }

        .user-title h1 {
            font-size: 36px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .archetype {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .stats-bar {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .persona-content {
            grid-area: content;
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-content: start;
        }

        .section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #FF6B6B;
        }

        .section h2 {
            color: #FF6B6B;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 24px;
            height: 24px;
            background: #FF6B6B;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .behavior-list, .frustration-list, .goals-list {
            list-style: none;
        }

        .behavior-list li, .frustration-list li, .goals-list li {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 3px solid #4ECDC4;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .behavior-list li:hover, .frustration-list li:hover, .goals-list li:hover {
            transform: translateX(5px);
        }

        .motivations {
            grid-column: span 2;
        }

        .motivation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .motivation-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .motivation-label {
            font-size: 14px;
            font-weight: bold;
            color: #666;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(#FF6B6B var(--progress), #e9ecef var(--progress));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
        }

        .progress-circle::before {
            content: '';
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }

        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: #FF6B6B;
        }

        .data-insights {
            grid-area: insights;
            background: #f8f9fa;
            padding: 30px;
            border-left: 1px solid #e9ecef;
        }

        .data-insights h3 {
            color: #FF6B6B;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .insight-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .persona-footer {
            grid-area: footer;
            background: #2c3e50;
            color: white;
            padding: 20px 40px;
            text-align: center;
            font-size: 14px;
        }

        @media (max-width: 1024px) {
            .persona-container {
                grid-template-areas:
                    "header"
                    "content"
                    "insights"
                    "footer";
                grid-template-columns: 1fr;
            }

            .persona-content {
                grid-template-columns: 1fr;
            }

            .motivations {
                grid-column: span 1;
            }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    
    </style>
</head>
<body>
    <div class="persona-container">
        <header class="persona-header">
            <div class="user-avatar" data-style="modern">
                H
            </div>
            <div class="user-title">
                <h1>Hungry-Move-6603</h1>
                <p class="archetype">Frustrated Expatriate</p>
                <div class="stats-bar">
                    
        <div class="stat-item">
            <span class="stat-number">3</span>
            <span class="stat-label">Posts</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">12</span>
            <span class="stat-label">Comments</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">30</span>
            <span class="stat-label">Karma</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">6</span>
            <span class="stat-label">Communities</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">1562</span>
            <span class="stat-label">Days Old</span>
        </div>
    
                </div>
            </div>
        </header>

        <main class="persona-content">
            
            <div class="section fade-in">
                <h2><span class="section-icon">👤</span>Basic Information</h2>
                <div class='info-item'><strong>Age:</strong> Business Owner/Entrepreneur (Likely in early stages or recently relocated)</div><div class='info-item'><strong>Occupation:</strong> Business Owner/Entrepreneur (Likely in early stages or recently relocated)</div><div class='info-item'><strong>Status:</strong> Single/Newly Relocated</div><div class='info-item'><strong>Location:</strong> Lucknow, Uttar Pradesh, India</div><div class='info-item'><strong>Archetype:</strong> Frustrated Expatriate</div>
            </div>
        
            <div class="section fade-in">
                <h2><span class="section-icon">🎯</span>Behaviors & Habits</h2>
                <ul class='behavior-list'><li>- Actively seeks recommendations and information about local activities and amenities: "Productive weekend activities in LKO?" <small class='citation'>[Hypothetical Reddit URL for the post]</small></li><li>- Expresses opinions and observations about the local culture and social norms, comparing them to previous experiences: "Everyone is something in LKO. Born and raised in Delhi..." <small class='citation'>[Hypothetical Reddit URL for the post]</small></li><li>- Engages in discussions related to food, health, and practical solutions for everyday challenges: "A menu easy to cook/process - healthy and quick." <small class='citation'>[Hypothetical Reddit URL for the comment]</small></li><li>Browses Reddit during work hours, suggesting a flexible work schedule or potential boredom: "Most Active Hour: 10:00" </li></ul>
            </div>
        
            <div class="section fade-in">
                <h2><span class="section-icon">😤</span>Frustrations</h2>
                <ul class='frustration-list'><li>- Dissatisfied with the perceived corruption and bribery: "Cops keep a civ around to discuss bribes."</li><li>- Annoyed by what they perceive as excessive displays of authority or status: "The number of cars having these stickers is way too high compared to Delhi, or anywhere else."</li><li>- Struggles to find healthy and affordable food options: "I even purchased a 250 per meal tiffin and it had palm oil!!!"</li><li>- Dislikes the current state of malls and rentals, pointing to a lack of demand and high costs: "Malls are a thing of past - and entire LKO is on steroids in rents cost, despite low to no demand."</li></ul>
            </div>
        
            <div class="section fade-in">
                <h2><span class="section-icon">🎯</span>Goals & Needs</h2>
                <ul class='goals-list'><li>To integrate into the local community while maintaining personal standards and values.</li><li>To find convenient, healthy, and affordable food options.</li><li>To navigate the local business and social environment successfully.</li><li>To establish a comfortable and fulfilling life in Lucknow.</li></ul>
            </div>
        
        <div class="section motivations fade-in">
            <h2><span class="section-icon">⚡</span>Motivations</h2>
            <div class='motivation-grid'>
            <div class="motivation-item">
                <div class="motivation-label">Convenience</div>
                <div class="progress-circle" style="--progress: 324.0deg">
                    <div class="progress-text">9/10</div>
                </div>
            </div>
        
            <div class="motivation-item">
                <div class="motivation-label">Wellness</div>
                <div class="progress-circle" style="--progress: 108.0deg">
                    <div class="progress-text">3/10</div>
                </div>
            </div>
        
            <div class="motivation-item">
                <div class="motivation-label">Speed</div>
                <div class="progress-circle" style="--progress: 360.0deg">
                    <div class="progress-text">10/10</div>
                </div>
            </div>
        
            <div class="motivation-item">
                <div class="motivation-label">Preferences</div>
                <div class="progress-circle" style="--progress: 216.0deg">
                    <div class="progress-text">6/10</div>
                </div>
            </div>
        
            <div class="motivation-item">
                <div class="motivation-label">Comfort</div>
                <div class="progress-circle" style="--progress: 251.99999999999997deg">
                    <div class="progress-text">7/10</div>
                </div>
            </div>
        </div>
        </div>
    
        </main>

        <aside class="data-insights">
            <h3>📊 Data Insights</h3>
            <div class='insight-card'><h4>Top Communities</h4><ul><li>r/lucknow <span style='float: right'>9 (60.0%)</span></li><li>r/nagpur <span style='float: right'>2 (13.3%)</span></li><li>r/delhi <span style='float: right'>1 (6.7%)</span></li><li>r/IndiaUnfilter <span style='float: right'>1 (6.7%)</span></li><li>r/indiasocial <span style='float: right'>1 (6.7%)</span></li></ul></div>
            <div class='insight-card'>
                <h4>Activity Pattern</h4>
                <p>Most active at <strong>10:00</strong></p>
                <p>Total posts: <strong>3</strong></p>
                <p>Total comments: <strong>12</strong></p>
            </div>
        
        <div class='insight-card'>
            <h4>Communication Style</h4>
            <p>Positive: <strong>10.6%</strong></p>
            <p>Neutral: <strong>77.6%</strong></p>
            <p>Negative: <strong>11.8%</strong></p>
        </div>
    <div class='insight-card'><h4>Common Words</h4><ul><li>to <span style='float: right'>9</span></li><li>a <span style='float: right'>8</span></li><li>and <span style='float: right'>7</span></li><li>of <span style='float: right'>6</span></li><li>i <span style='float: right'>6</span></li></ul></div>
        </aside>

        <footer class="persona-footer">
            <p>Generated on July 16, 2025 | Based on 3 posts and 12 comments | Unique persona for Hungry-Move-6603</p>
        </footer>
    </div>

    <script>
        
        // Add fade-in animation on load
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Add hover effects for interactive elements
        document.querySelectorAll('.behavior-list li, .frustration-list li, .goals-list li').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#e3f2fd';
            });
            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'white';
            });
        });

        // Add click to copy functionality for citations
        document.querySelectorAll('.citation').forEach(citation => {
            citation.style.cursor = 'pointer';
            citation.title = 'Click to copy URL';
            citation.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent);
                this.style.color = '#4CAF50';
                setTimeout(() => {
                    this.style.color = '';
                }, 1000);
            });
        });
    
    </script>
</body>
</html>