<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hungry-Move-6603 - User Persona</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .persona-card {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 800px;
        }
        
        .profile-section {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin-bottom: 20px;
            border: 4px solid rgba(255,255,255,0.3);
        }
        
        .username {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .basic-info {
            margin-top: 30px;
            text-align: left;
            width: 100%;
        }
        
        .basic-info h3 {
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 5px;
        }
        
        .info-item {
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .info-label {
            font-weight: bold;
            opacity: 0.8;
        }
        
        .content-section {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #333;
            font-size: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #FF6B6B;
            padding-left: 15px;
        }
        
        .behavior-list, .frustration-list, .goals-list {
            list-style: none;
        }
        
        .behavior-list li, .frustration-list li, .goals-list li {
            background: #f8f9fa;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 3px solid #FF6B6B;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .motivations {
            grid-column: span 2;
        }
        
        .motivation-bars {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .motivation-item {
            text-align: center;
        }
        
        .motivation-label {
            font-size: 12px;
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .progress-bar {
            width: 100%;
            height: 120px;
            background: #e9ecef;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .progress-fill {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            border-radius: 10px;
            transition: height 0.8s ease;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .personality {
            grid-column: span 2;
        }
        
        .personality-traits {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .trait-scale {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .trait-label {
            font-size: 12px;
            font-weight: bold;
            color: #666;
            min-width: 80px;
        }
        
        .scale-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            position: relative;
        }
        
        .scale-indicator {
            position: absolute;
            top: -2px;
            width: 12px;
            height: 12px;
            background: #FF6B6B;
            border-radius: 50%;
            transform: translateX(-50%);
        }
        
        .quote {
            font-style: italic;
            color: #666;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #FF6B6B;
            margin: 20px 0;
            grid-column: span 2;
        }
        
        .citation {
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .persona-card {
                grid-template-columns: 1fr;
                margin: 10px;
            }
            
            .content-section {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .motivations, .personality {
                grid-column: span 1;
            }
            
            .motivation-bars {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="persona-card">
        <div class="profile-section">
            <div class="avatar">H</div>
            <div class="username">Hungry-Move-6603</div>
            
            <div class="basic-info">
                <h3>BASIC INFO</h3>
                <div class="info-item">
                    <span class="info-label">AGE:</span> 30-35
                </div>
                <div class="info-item">
                    <span class="info-label">OCCUPATION:</span> Business Owner/Manager (Likely Small to Medium Enterprise)
                </div>
                <div class="info-item">
                    <span class="info-label">STATUS:</span> Single/Recently Relocated Professional
                </div>
                <div class="info-item">
                    <span class="info-label">LOCATION:</span> Lucknow, Uttar Pradesh, India
                </div>
                <div class="info-item">
                    <span class="info-label">ARCHETYPE:</span> Discontented Expatriate
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <div class="section">
                <h2>BEHAVIOUR & HABITS</h2>
                <ul class="behavior-list">
                    <li>Malls are a thing of past - and entire LKO is on steroids in rents cost, despite low to no demand.... <em>(lucknow)</em></li><li>I was caught without helmet and license (close to my home). Cops outright wanted to fine me, but a '... <em>(nagpur)</em></li>
                </ul>
            </div>
            
            <div class="section">
                <h2>FRUSTRATIONS</h2>
                <ul class="frustration-list">
                    <li>I was caught without helmet and license (close to my home). Cops outright wanted to fine me, but a '...</li><li>Haha Delhi is hateable too but mostly those are HR or UP14 NCR vehicle. Plus the percentage of vehic...</li><li>Never seen anything of this sort in capital city of India. So yes I did not expect 🤪...</li>
                </ul>
            </div>
            
            <div class="section motivations">
                <h2>MOTIVATIONS</h2>
                <div class="motivation-bars">
                    
                <div class="motivation-item">
                    <div class="motivation-label">CONVENIENCE</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: 50%"></div>
                        <div class="progress-text">5/10</div>
                    </div>
                </div>
            
                <div class="motivation-item">
                    <div class="motivation-label">WELLNESS</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: 30%"></div>
                        <div class="progress-text">3/10</div>
                    </div>
                </div>
            
                <div class="motivation-item">
                    <div class="motivation-label">SPEED</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: 80%"></div>
                        <div class="progress-text">8/10</div>
                    </div>
                </div>
            
                <div class="motivation-item">
                    <div class="motivation-label">PREFERENCES</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: 60%"></div>
                        <div class="progress-text">6/10</div>
                    </div>
                </div>
            
                <div class="motivation-item">
                    <div class="motivation-label">COMFORT</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: 30%"></div>
                        <div class="progress-text">3/10</div>
                    </div>
                </div>
            
                </div>
            </div>
            
            <div class="section personality">
                <h2>PERSONALITY</h2>
                <div class="personality-traits">
                    
                <div class="trait-scale">
                    <div class="trait-label">INTROVERT</div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: 30%"></div>
                    </div>
                    <div class="trait-label">EXTROVERT</div>
                </div>
            
                <div class="trait-scale">
                    <div class="trait-label">INTUITION</div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: 70%"></div>
                    </div>
                    <div class="trait-label">SENSING</div>
                </div>
            
                <div class="trait-scale">
                    <div class="trait-label">FEELING</div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: 60%"></div>
                    </div>
                    <div class="trait-label">THINKING</div>
                </div>
            
                <div class="trait-scale">
                    <div class="trait-label">PERCEIVING</div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: 40%"></div>
                    </div>
                    <div class="trait-label">JUDGING</div>
                </div>
            
                </div>
            </div>
            
            <div class="section">
                <h2>GOALS & NEEDS</h2>
                <ul class="goals-list">
                    
            <li>Engage with lucknow community discussions</li>
            <li>Share knowledge and experiences with fellow Redditors</li>
            <li>Stay updated on topics of interest through Reddit communities</li>
        
                </ul>
            </div>
            
            <div class="quote">
                "Active Reddit user with 3 posts and 12 comments"
                <div class="citation">— Based on Reddit activity analysis</div>
            </div>
        </div>
    </div>
</body>
</html>
