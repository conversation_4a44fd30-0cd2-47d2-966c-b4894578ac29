import os
import re
import praw
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()

# Configure Reddit API
reddit = praw.Reddit(
    client_id=os.getenv("REDDIT_CLIENT_ID"),
    client_secret=os.getenv("REDDIT_CLIENT_SECRET"),
    user_agent=os.getenv("REDDIT_USER_AGENT"),
)

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
# Print available models to help debug model name issues
print("Available Gemini models:")
model = genai.GenerativeModel('gemini-2.0-flash')  # fast and efficient 


def extract_username(profile_url):
    match = re.search(r"reddit\.com\/user\/([A-Za-z0-9_-]+)", profile_url)
    return match.group(1) if match else None

def scrape_user_data(username, limit=50):
    redditor = reddit.redditor(username)
    posts = []
    comments = []

    for post in redditor.submissions.new(limit=limit):
        content = f"Title: {post.title}\nBody: {post.selftext.strip()}\nURL: https://reddit.com{post.permalink}"
        posts.append(content)

    for comment in redditor.comments.new(limit=limit):
        content = f"Comment: {comment.body.strip()}\nURL: https://reddit.com{comment.permalink}"
        comments.append(content)

    return posts, comments

def generate_persona(posts, comments):
    prompt = f"""
You are an expert in analyzing online behavior. Based on the following Reddit user's activity, create a detailed **User Persona**.

**Include the following attributes**:
- Age Range
- Occupation (guess from behavior)
- Hobbies and Interests
- Personality Traits
- Social/Political Leanings (if possible)
- Preferred Reddit Communities
- Writing Style or Tone

🔍 For **each trait**, also include a **short quote** from the user's Reddit post or comment with the **URL** as a citation.

Format the output like:

Trait: Description  
Cited from: "quoted text" — URL

---

### USER POSTS:
{chr(10).join(posts)}

### USER COMMENTS:
{chr(10).join(comments)}
"""

    response = model.generate_content(prompt)
    return response.text

def save_to_file(username, persona_text):
    os.makedirs("output", exist_ok=True)
    file_path = f"output/{username}_persona.txt"
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(persona_text)
    print(f"\n✅ User persona saved to {file_path}")

def main():
    profile_url = input("Paste Reddit profile URL: ").strip()
    username = extract_username(profile_url)

    if not username:
        print("❌ Invalid Reddit profile URL.")
        return

    print(f"🔎 Scraping data for user: {username}...")
    posts, comments = scrape_user_data(username)

    print(f"🤖 Generating user persona using Gemini AI...")
    persona_text = generate_persona(posts, comments)

    save_to_file(username, persona_text)

if __name__ == "__main__":
    main()
