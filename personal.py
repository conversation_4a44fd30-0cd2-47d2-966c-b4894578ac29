import os
import re
import json
import praw
import google.generativeai as genai
from dotenv import load_dotenv
from datetime import datetime
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
import pandas as pd
from textstat import flesch_reading_ease
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer

# Download required NLTK data
try:
    nltk.data.find('vader_lexicon')
except LookupError:
    nltk.download('vader_lexicon')

load_dotenv()

# Configure Reddit API
reddit = praw.Reddit(
    client_id=os.getenv("REDDIT_CLIENT_ID"),
    client_secret=os.getenv("REDDIT_CLIENT_SECRET"),
    user_agent=os.getenv("REDDIT_USER_AGENT"),
)

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
model = genai.GenerativeModel('gemini-2.0-flash-exp')  # Using experimental for better analysis

# Initialize sentiment analyzer
sia = SentimentIntensityAnalyzer()


def extract_username(profile_url):
    match = re.search(r"reddit\.com\/user\/([A-Za-z0-9_-]+)", profile_url)
    return match.group(1) if match else None

def analyze_text_metrics(text_list):
    """Analyze writing style and sentiment from text"""
    if not text_list:
        return {}

    combined_text = " ".join(text_list)

    # Sentiment analysis
    sentiment_scores = [sia.polarity_scores(text) for text in text_list]
    avg_sentiment = {
        'positive': sum(s['pos'] for s in sentiment_scores) / len(sentiment_scores),
        'negative': sum(s['neg'] for s in sentiment_scores) / len(sentiment_scores),
        'neutral': sum(s['neu'] for s in sentiment_scores) / len(sentiment_scores),
        'compound': sum(s['compound'] for s in sentiment_scores) / len(sentiment_scores)
    }

    # Reading level
    try:
        reading_ease = flesch_reading_ease(combined_text)
    except:
        reading_ease = 50  # Default middle value

    # Word frequency
    words = re.findall(r'\b\w+\b', combined_text.lower())
    word_freq = Counter(words).most_common(20)

    return {
        'sentiment': avg_sentiment,
        'reading_ease': reading_ease,
        'word_frequency': word_freq,
        'total_words': len(words),
        'avg_words_per_post': len(words) / len(text_list) if text_list else 0
    }

def scrape_user_data(username, limit=100):
    """Enhanced data scraping with detailed analysis"""
    print(f"🔍 Analyzing user: {username}")

    try:
        redditor = reddit.redditor(username)

        # Basic user info
        user_info = {
            'username': username,
            'created_utc': redditor.created_utc,
            'comment_karma': redditor.comment_karma,
            'link_karma': redditor.link_karma,
            'account_age_days': (datetime.now().timestamp() - redditor.created_utc) / 86400
        }

        posts_data = []
        comments_data = []
        subreddits = []
        posting_times = []

        print("📝 Scraping posts...")
        for post in redditor.submissions.new(limit=limit):
            post_data = {
                'title': post.title,
                'body': post.selftext.strip(),
                'url': f"https://reddit.com{post.permalink}",
                'subreddit': str(post.subreddit),
                'score': post.score,
                'created_utc': post.created_utc,
                'num_comments': post.num_comments
            }
            posts_data.append(post_data)
            subreddits.append(str(post.subreddit))
            posting_times.append(datetime.fromtimestamp(post.created_utc).hour)

        print("💬 Scraping comments...")
        for comment in redditor.comments.new(limit=limit):
            comment_data = {
                'body': comment.body.strip(),
                'url': f"https://reddit.com{comment.permalink}",
                'subreddit': str(comment.subreddit),
                'score': comment.score,
                'created_utc': comment.created_utc
            }
            comments_data.append(comment_data)
            subreddits.append(str(comment.subreddit))
            posting_times.append(datetime.fromtimestamp(comment.created_utc).hour)

        # Analyze patterns
        subreddit_freq = Counter(subreddits).most_common(10)
        posting_hour_freq = Counter(posting_times)

        # Text analysis
        post_texts = [f"{p['title']} {p['body']}" for p in posts_data if p['title'] or p['body']]
        comment_texts = [c['body'] for c in comments_data if c['body']]

        post_metrics = analyze_text_metrics(post_texts)
        comment_metrics = analyze_text_metrics(comment_texts)

        return {
            'user_info': user_info,
            'posts': posts_data,
            'comments': comments_data,
            'subreddit_activity': subreddit_freq,
            'posting_patterns': {
                'hours': dict(posting_hour_freq),
                'most_active_hour': max(posting_hour_freq, key=posting_hour_freq.get) if posting_hour_freq else 12
            },
            'text_analysis': {
                'posts': post_metrics,
                'comments': comment_metrics
            }
        }

    except Exception as e:
        print(f"❌ Error scraping user data: {e}")
        return None

def generate_professional_persona(user_data):
    """Generate a professional persona in the style of the Lucas Mellor example"""

    if not user_data:
        return "Error: No user data available"

    # Prepare data for AI analysis
    posts_text = []
    comments_text = []
    citations = []

    for post in user_data['posts'][:20]:  # Limit for token efficiency
        if post['title'] or post['body']:
            text = f"Title: {post['title']}\nBody: {post['body']}"
            posts_text.append(text)
            citations.append({
                'text': text[:200] + "..." if len(text) > 200 else text,
                'url': post['url'],
                'type': 'post'
            })

    for comment in user_data['comments'][:30]:  # More comments for personality analysis
        if comment['body']:
            comments_text.append(comment['body'])
            citations.append({
                'text': comment['body'][:200] + "..." if len(comment['body']) > 200 else comment['body'],
                'url': comment['url'],
                'type': 'comment'
            })

    # Create comprehensive prompt
    prompt = f"""
You are an expert UX researcher creating a detailed user persona. Based on the Reddit user's activity data, create a professional persona similar to the Lucas Mellor example.

**USER DATA ANALYSIS:**
- Username: {user_data['user_info']['username']}
- Account Age: {user_data['user_info']['account_age_days']:.0f} days
- Comment Karma: {user_data['user_info']['comment_karma']}
- Link Karma: {user_data['user_info']['link_karma']}
- Top Subreddits: {', '.join([sub[0] for sub in user_data['subreddit_activity'][:5]])}
- Most Active Hour: {user_data['posting_patterns']['most_active_hour']}:00
- Sentiment Analysis: {user_data['text_analysis']['comments']['sentiment']['compound']:.2f} (compound score)
- Writing Complexity: {user_data['text_analysis']['comments']['reading_ease']:.0f} (Flesch Reading Ease)

**RECENT POSTS:**
{chr(10).join(posts_text[:10])}

**RECENT COMMENTS:**
{chr(10).join(comments_text[:15])}

**INSTRUCTIONS:**
Create a professional user persona with these EXACT sections:

1. **BASIC INFO** (estimate based on behavior):
   - Age: [age range]
   - Occupation: [educated guess]
   - Status: [relationship/life status]
   - Location: [general region if detectable]
   - Archetype: [user type in 2-3 words]

2. **BEHAVIOR & HABITS** (3-4 bullet points):
   - Specific behavioral patterns observed
   - Each point should cite a specific post/comment

3. **FRUSTRATIONS** (3-4 bullet points):
   - What annoys or frustrates this user
   - Based on complaints or negative sentiment

4. **MOTIVATIONS** (list 5 items with estimated strength 1-10):
   - CONVENIENCE: [score]/10
   - WELLNESS: [score]/10
   - SPEED: [score]/10
   - PREFERENCES: [score]/10
   - COMFORT: [score]/10

5. **PERSONALITY TRAITS** (4 scales):
   - INTROVERT ←→ EXTROVERT
   - INTUITION ←→ SENSING
   - FEELING ←→ THINKING
   - PERCEIVING ←→ JUDGING

6. **GOALS & NEEDS** (3-4 bullet points):
   - What they're trying to achieve
   - Their primary needs and desires

**CITATION FORMAT:**
For each behavioral observation, include: "Quote from their content" — [URL]

**TONE:** Professional, analytical, based on evidence. Avoid speculation without basis.
"""

    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        return f"Error generating persona: {e}"

def create_visualizations(user_data, username):
    """Create data visualizations for the user"""
    try:
        os.makedirs(f"output/{username}_analysis", exist_ok=True)

        # Set style
        plt.style.use('seaborn-v0_8')

        # 1. Subreddit Activity Chart
        if user_data['subreddit_activity']:
            fig, ax = plt.subplots(figsize=(12, 6))
            subreddits = [item[0] for item in user_data['subreddit_activity'][:10]]
            counts = [item[1] for item in user_data['subreddit_activity'][:10]]

            bars = ax.barh(subreddits, counts, color='#FF4500')
            ax.set_xlabel('Number of Posts/Comments')
            ax.set_title(f'{username} - Top Subreddit Activity')
            ax.grid(axis='x', alpha=0.3)

            # Add value labels
            for bar in bars:
                width = bar.get_width()
                ax.text(width, bar.get_y() + bar.get_height()/2,
                       f'{int(width)}', ha='left', va='center')

            plt.tight_layout()
            plt.savefig(f"output/{username}_analysis/subreddit_activity.png", dpi=300, bbox_inches='tight')
            plt.close()

        # 2. Posting Time Heatmap
        if user_data['posting_patterns']['hours']:
            fig, ax = plt.subplots(figsize=(12, 3))
            hours = list(range(24))
            activity = [user_data['posting_patterns']['hours'].get(h, 0) for h in hours]

            # Create heatmap data
            heatmap_data = [activity]

            sns.heatmap(heatmap_data,
                       xticklabels=[f"{h:02d}:00" for h in hours],
                       yticklabels=['Activity'],
                       cmap='Reds',
                       annot=False,
                       cbar_kws={'label': 'Posts/Comments'},
                       ax=ax)

            ax.set_title(f'{username} - Posting Activity by Hour (24h format)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"output/{username}_analysis/posting_times.png", dpi=300, bbox_inches='tight')
            plt.close()

        # 3. Word Cloud
        if user_data['text_analysis']['comments']['word_frequency']:
            # Combine all text
            all_text = []
            for comment in user_data['comments'][:50]:
                if comment['body'] and len(comment['body']) > 10:
                    all_text.append(comment['body'])

            if all_text:
                combined_text = ' '.join(all_text)
                # Remove common Reddit words
                stop_words = {'reddit', 'post', 'comment', 'like', 'think', 'know', 'get', 'would', 'could', 'should', 'really', 'much', 'also', 'even', 'still', 'way', 'make', 'take', 'come', 'go', 'see', 'look', 'want', 'need', 'say', 'said', 'tell', 'ask', 'give', 'find', 'use', 'work', 'try', 'feel', 'seem', 'become', 'leave', 'put', 'mean', 'keep', 'let', 'begin', 'seem', 'help', 'talk', 'turn', 'start', 'might', 'show', 'hear', 'play', 'run', 'move', 'live', 'believe', 'bring', 'happen', 'write', 'provide', 'sit', 'stand', 'lose', 'pay', 'meet', 'include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'love', 'consider', 'appear', 'buy', 'wait', 'serve', 'die', 'send', 'expect', 'build', 'stay', 'fall', 'cut', 'reach', 'kill', 'remain'}

                wordcloud = WordCloud(width=800, height=400,
                                    background_color='white',
                                    stopwords=stop_words,
                                    max_words=100,
                                    colormap='viridis').generate(combined_text)

                plt.figure(figsize=(12, 6))
                plt.imshow(wordcloud, interpolation='bilinear')
                plt.axis('off')
                plt.title(f'{username} - Most Common Words', fontsize=16, pad=20)
                plt.tight_layout()
                plt.savefig(f"output/{username}_analysis/wordcloud.png", dpi=300, bbox_inches='tight')
                plt.close()

        print(f"📊 Visualizations saved to output/{username}_analysis/")

    except Exception as e:
        print(f"⚠️ Error creating visualizations: {e}")

def generate_html_persona(username, user_data, persona_text):
    """Generate a visual HTML persona card"""
    try:
        # Parse the persona text to extract structured data
        # This is a simplified parser - you might want to enhance it
        lines = persona_text.split('\n')

        # Default values
        persona_data = {
            'username': username,
            'avatar_initial': username[0].upper() if username else 'U',
            'age': 'Unknown',
            'occupation': 'Unknown',
            'status': 'Unknown',
            'location': 'Unknown',
            'archetype': 'Reddit User',
            'behavior_items': '',
            'frustration_items': '',
            'motivation_bars': '',
            'personality_traits': '',
            'goals_items': '',
            'user_quote': f"Active Reddit user with {len(user_data['posts'])} posts and {len(user_data['comments'])} comments"
        }

        # Try to extract data from persona text
        current_section = None
        for line in lines:
            line = line.strip()
            if 'Age:' in line or 'AGE:' in line:
                persona_data['age'] = line.split(':')[1].strip()
            elif 'Occupation:' in line or 'OCCUPATION:' in line:
                persona_data['occupation'] = line.split(':')[1].strip()
            elif 'Status:' in line or 'STATUS:' in line:
                persona_data['status'] = line.split(':')[1].strip()
            elif 'Location:' in line or 'LOCATION:' in line:
                persona_data['location'] = line.split(':')[1].strip()
            elif 'Archetype:' in line or 'ARCHETYPE:' in line:
                persona_data['archetype'] = line.split(':')[1].strip()

        # Generate motivation bars (using sample data based on analysis)
        motivations = [
            ('CONVENIENCE', min(10, max(1, int(user_data['text_analysis']['comments']['sentiment']['compound'] * 5 + 5)))),
            ('WELLNESS', min(10, max(1, len([s for s in user_data['subreddit_activity'] if 'health' in s[0].lower() or 'fitness' in s[0].lower()]) + 3))),
            ('SPEED', min(10, max(1, 10 - int(user_data['text_analysis']['comments']['avg_words_per_post'] / 10)))),
            ('PREFERENCES', min(10, max(1, len(user_data['subreddit_activity'])))),
            ('COMFORT', min(10, max(1, int(user_data['posting_patterns']['most_active_hour'] / 3))))
        ]

        motivation_html = ''
        for label, score in motivations:
            motivation_html += f'''
                <div class="motivation-item">
                    <div class="motivation-label">{label}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="height: {score * 10}%"></div>
                        <div class="progress-text">{score}/10</div>
                    </div>
                </div>
            '''
        persona_data['motivation_bars'] = motivation_html

        # Generate personality traits (sample positioning)
        traits = [
            ('INTROVERT', 'EXTROVERT', 30),  # Based on posting frequency
            ('INTUITION', 'SENSING', 70),    # Based on abstract vs concrete language
            ('FEELING', 'THINKING', 60),     # Based on sentiment analysis
            ('PERCEIVING', 'JUDGING', 40)   # Based on posting patterns
        ]

        traits_html = ''
        for left, right, position in traits:
            traits_html += f'''
                <div class="trait-scale">
                    <div class="trait-label">{left}</div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: {position}%"></div>
                    </div>
                    <div class="trait-label">{right}</div>
                </div>
            '''
        persona_data['personality_traits'] = traits_html

        # Generate behavior items from top comments
        behavior_items = ''
        for i, comment in enumerate(user_data['comments'][:3]):
            if comment['body'] and len(comment['body']) > 20:
                behavior_items += f'<li>{comment["body"][:100]}... <em>({comment["subreddit"]})</em></li>'
        persona_data['behavior_items'] = behavior_items

        # Generate frustration items (negative sentiment comments)
        frustration_items = ''
        negative_comments = [c for c in user_data['comments'] if 'not' in c['body'].lower() or 'hate' in c['body'].lower() or 'annoying' in c['body'].lower()][:3]
        for comment in negative_comments:
            if comment['body'] and len(comment['body']) > 20:
                frustration_items += f'<li>{comment["body"][:100]}...</li>'
        if not frustration_items:
            frustration_items = '<li>Analysis of posting patterns suggests general satisfaction with Reddit experience</li>'
        persona_data['frustration_items'] = frustration_items

        # Generate goals items
        goals_items = f'''
            <li>Engage with {user_data['subreddit_activity'][0][0] if user_data['subreddit_activity'] else 'various'} community discussions</li>
            <li>Share knowledge and experiences with fellow Redditors</li>
            <li>Stay updated on topics of interest through Reddit communities</li>
        '''
        persona_data['goals_items'] = goals_items

        # Read template and replace placeholders
        with open('persona_template.html', 'r', encoding='utf-8') as f:
            template = f.read()

        for key, value in persona_data.items():
            template = template.replace(f'{{{{{key}}}}}', str(value))

        # Save HTML file
        html_path = f"output/{username}_persona.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(template)

        print(f"🎨 Visual persona card saved: {html_path}")
        return html_path

    except Exception as e:
        print(f"⚠️ Error generating HTML persona: {e}")
        return None

def save_comprehensive_report(username, user_data, persona_text):
    """Save comprehensive report with all data"""
    os.makedirs("output", exist_ok=True)

    # Save persona
    persona_path = f"output/{username}_persona.txt"
    with open(persona_path, "w", encoding="utf-8") as f:
        f.write(f"# USER PERSONA: {username.upper()}\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("=" * 80 + "\n\n")
        f.write(persona_text)
        f.write("\n\n" + "=" * 80 + "\n")
        f.write("## DATA ANALYSIS SUMMARY\n\n")

        # Add data summary
        f.write(f"**Account Statistics:**\n")
        f.write(f"- Username: {user_data['user_info']['username']}\n")
        f.write(f"- Account Age: {user_data['user_info']['account_age_days']:.0f} days\n")
        f.write(f"- Comment Karma: {user_data['user_info']['comment_karma']:,}\n")
        f.write(f"- Link Karma: {user_data['user_info']['link_karma']:,}\n")
        f.write(f"- Posts Analyzed: {len(user_data['posts'])}\n")
        f.write(f"- Comments Analyzed: {len(user_data['comments'])}\n\n")

        f.write(f"**Top Subreddits:**\n")
        for sub, count in user_data['subreddit_activity'][:10]:
            f.write(f"- r/{sub}: {count} posts/comments\n")

        f.write(f"\n**Behavioral Patterns:**\n")
        f.write(f"- Most Active Hour: {user_data['posting_patterns']['most_active_hour']}:00\n")
        f.write(f"- Average Sentiment: {user_data['text_analysis']['comments']['sentiment']['compound']:.2f}\n")
        f.write(f"- Writing Complexity: {user_data['text_analysis']['comments']['reading_ease']:.0f} (Flesch Reading Ease)\n")

        f.write(f"\n**Citations and Sources:**\n")
        f.write("All persona traits are derived from the user's actual Reddit posts and comments.\n")
        f.write("URLs are provided for verification and deeper analysis.\n")

    # Save raw data as JSON
    json_path = f"output/{username}_raw_data.json"
    with open(json_path, "w", encoding="utf-8") as f:
        # Convert datetime objects to strings for JSON serialization
        json_data = user_data.copy()
        for post in json_data['posts']:
            post['created_utc'] = datetime.fromtimestamp(post['created_utc']).isoformat()
        for comment in json_data['comments']:
            comment['created_utc'] = datetime.fromtimestamp(comment['created_utc']).isoformat()
        json_data['user_info']['created_utc'] = datetime.fromtimestamp(json_data['user_info']['created_utc']).isoformat()

        json.dump(json_data, f, indent=2, ensure_ascii=False)

    print(f"\n✅ Comprehensive report saved:")
    print(f"   📄 Persona: {persona_path}")
    print(f"   📊 Raw Data: {json_path}")

def main():
    print("🎯 REDDIT USER PERSONA GENERATOR")
    print("=" * 50)

    profile_url = input("Paste Reddit profile URL: ").strip()
    username = extract_username(profile_url)

    if not username:
        print("❌ Invalid Reddit profile URL.")
        print("Expected format: https://www.reddit.com/user/username/")
        return

    print(f"\n🔎 Starting comprehensive analysis for: {username}")
    print("This may take a few minutes...")

    # Scrape and analyze data
    user_data = scrape_user_data(username, limit=150)

    if not user_data:
        print("❌ Failed to scrape user data. Check username and try again.")
        return

    print(f"✅ Data collection complete!")
    print(f"   📝 Posts: {len(user_data['posts'])}")
    print(f"   💬 Comments: {len(user_data['comments'])}")
    print(f"   🏷️ Subreddits: {len(user_data['subreddit_activity'])}")

    # Generate persona
    print(f"\n🤖 Generating professional persona...")
    persona_text = generate_professional_persona(user_data)

    # Create visualizations
    print(f"📊 Creating data visualizations...")
    create_visualizations(user_data, username)

    # Generate HTML persona card
    print(f"🎨 Creating visual persona card...")
    html_path = generate_html_persona(username, user_data, persona_text)

    # Save everything
    save_comprehensive_report(username, user_data, persona_text)

    print(f"\n🎉 Analysis complete for {username}!")
    print(f"📁 Generated files:")
    print(f"   📄 Text Persona: output/{username}_persona.txt")
    print(f"   🎨 Visual Card: output/{username}_persona.html")
    print(f"   📊 Charts: output/{username}_analysis/")
    print(f"   📋 Raw Data: output/{username}_raw_data.json")

    if html_path:
        print(f"\n💡 Open {html_path} in your browser to see the visual persona card!")

if __name__ == "__main__":
    main()
