import os
import re
import json
import praw
import google.generativeai as genai
from dotenv import load_dotenv
from datetime import datetime
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
import pandas as pd
from textstat import flesch_reading_ease
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer

# Download required NLTK data
try:
    nltk.data.find('vader_lexicon')
except LookupError:
    nltk.download('vader_lexicon')

load_dotenv()

# Configure Reddit API
reddit = praw.Reddit(
    client_id=os.getenv("REDDIT_CLIENT_ID"),
    client_secret=os.getenv("REDDIT_CLIENT_SECRET"),
    user_agent=os.getenv("REDDIT_USER_AGENT"),
)

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
model = genai.GenerativeModel('gemini-2.0-flash-exp')  # Using experimental for better analysis

# Initialize sentiment analyzer
sia = SentimentIntensityAnalyzer()


def extract_username(profile_url):
    match = re.search(r"reddit\.com\/user\/([A-Za-z0-9_-]+)", profile_url)
    return match.group(1) if match else None

def analyze_text_metrics(text_list):
    """Analyze writing style and sentiment from text"""
    if not text_list:
        return {}

    combined_text = " ".join(text_list)

    # Sentiment analysis
    sentiment_scores = [sia.polarity_scores(text) for text in text_list]
    avg_sentiment = {
        'positive': sum(s['pos'] for s in sentiment_scores) / len(sentiment_scores),
        'negative': sum(s['neg'] for s in sentiment_scores) / len(sentiment_scores),
        'neutral': sum(s['neu'] for s in sentiment_scores) / len(sentiment_scores),
        'compound': sum(s['compound'] for s in sentiment_scores) / len(sentiment_scores)
    }

    # Reading level
    try:
        reading_ease = flesch_reading_ease(combined_text)
    except:
        reading_ease = 50  # Default middle value

    # Word frequency
    words = re.findall(r'\b\w+\b', combined_text.lower())
    word_freq = Counter(words).most_common(20)

    return {
        'sentiment': avg_sentiment,
        'reading_ease': reading_ease,
        'word_frequency': word_freq,
        'total_words': len(words),
        'avg_words_per_post': len(words) / len(text_list) if text_list else 0
    }

def scrape_user_data(username, limit=100):
    """Enhanced data scraping with detailed analysis"""
    print(f"🔍 Analyzing user: {username}")

    try:
        redditor = reddit.redditor(username)

        # Basic user info
        user_info = {
            'username': username,
            'created_utc': redditor.created_utc,
            'comment_karma': redditor.comment_karma,
            'link_karma': redditor.link_karma,
            'account_age_days': (datetime.now().timestamp() - redditor.created_utc) / 86400
        }

        posts_data = []
        comments_data = []
        subreddits = []
        posting_times = []

        print("📝 Scraping posts...")
        for post in redditor.submissions.new(limit=limit):
            post_data = {
                'title': post.title,
                'body': post.selftext.strip(),
                'url': f"https://reddit.com{post.permalink}",
                'subreddit': str(post.subreddit),
                'score': post.score,
                'created_utc': post.created_utc,
                'num_comments': post.num_comments
            }
            posts_data.append(post_data)
            subreddits.append(str(post.subreddit))
            posting_times.append(datetime.fromtimestamp(post.created_utc).hour)

        print("💬 Scraping comments...")
        for comment in redditor.comments.new(limit=limit):
            comment_data = {
                'body': comment.body.strip(),
                'url': f"https://reddit.com{comment.permalink}",
                'subreddit': str(comment.subreddit),
                'score': comment.score,
                'created_utc': comment.created_utc
            }
            comments_data.append(comment_data)
            subreddits.append(str(comment.subreddit))
            posting_times.append(datetime.fromtimestamp(comment.created_utc).hour)

        # Analyze patterns
        subreddit_freq = Counter(subreddits).most_common(10)
        posting_hour_freq = Counter(posting_times)

        # Text analysis
        post_texts = [f"{p['title']} {p['body']}" for p in posts_data if p['title'] or p['body']]
        comment_texts = [c['body'] for c in comments_data if c['body']]

        post_metrics = analyze_text_metrics(post_texts)
        comment_metrics = analyze_text_metrics(comment_texts)

        return {
            'user_info': user_info,
            'posts': posts_data,
            'comments': comments_data,
            'subreddit_activity': subreddit_freq,
            'posting_patterns': {
                'hours': dict(posting_hour_freq),
                'most_active_hour': max(posting_hour_freq, key=posting_hour_freq.get) if posting_hour_freq else 12
            },
            'text_analysis': {
                'posts': post_metrics,
                'comments': comment_metrics
            }
        }

    except Exception as e:
        print(f"❌ Error scraping user data: {e}")
        return None

def generate_professional_persona(user_data):
    """Generate a professional persona in the style of the Lucas Mellor example"""

    if not user_data:
        return "Error: No user data available"

    # Prepare data for AI analysis
    posts_text = []
    comments_text = []
    citations = []

    for post in user_data['posts'][:20]:  # Limit for token efficiency
        if post['title'] or post['body']:
            text = f"Title: {post['title']}\nBody: {post['body']}"
            posts_text.append(text)
            citations.append({
                'text': text[:200] + "..." if len(text) > 200 else text,
                'url': post['url'],
                'type': 'post'
            })

    for comment in user_data['comments'][:30]:  # More comments for personality analysis
        if comment['body']:
            comments_text.append(comment['body'])
            citations.append({
                'text': comment['body'][:200] + "..." if len(comment['body']) > 200 else comment['body'],
                'url': comment['url'],
                'type': 'comment'
            })

    # Create comprehensive prompt
    prompt = f"""
You are an expert UX researcher creating a detailed user persona. Based on the Reddit user's activity data, create a professional persona similar to the Lucas Mellor example.

**USER DATA ANALYSIS:**
- Username: {user_data['user_info']['username']}
- Account Age: {user_data['user_info']['account_age_days']:.0f} days
- Comment Karma: {user_data['user_info']['comment_karma']}
- Link Karma: {user_data['user_info']['link_karma']}
- Top Subreddits: {', '.join([sub[0] for sub in user_data['subreddit_activity'][:5]])}
- Most Active Hour: {user_data['posting_patterns']['most_active_hour']}:00
- Sentiment Analysis: {user_data['text_analysis']['comments']['sentiment']['compound']:.2f} (compound score)
- Writing Complexity: {user_data['text_analysis']['comments']['reading_ease']:.0f} (Flesch Reading Ease)

**RECENT POSTS:**
{chr(10).join(posts_text[:10])}

**RECENT COMMENTS:**
{chr(10).join(comments_text[:15])}

**INSTRUCTIONS:**
Create a professional user persona with these EXACT sections:

1. **BASIC INFO** (estimate based on behavior):
   - Age: [age range]
   - Occupation: [educated guess]
   - Status: [relationship/life status]
   - Location: [general region if detectable]
   - Archetype: [user type in 2-3 words]

2. **BEHAVIOR & HABITS** (3-4 bullet points):
   - Specific behavioral patterns observed
   - Each point should cite a specific post/comment

3. **FRUSTRATIONS** (3-4 bullet points):
   - What annoys or frustrates this user
   - Based on complaints or negative sentiment

4. **MOTIVATIONS** (list 5 items with estimated strength 1-10):
   - CONVENIENCE: [score]/10
   - WELLNESS: [score]/10
   - SPEED: [score]/10
   - PREFERENCES: [score]/10
   - COMFORT: [score]/10

5. **PERSONALITY TRAITS** (4 scales):
   - INTROVERT ←→ EXTROVERT
   - INTUITION ←→ SENSING
   - FEELING ←→ THINKING
   - PERCEIVING ←→ JUDGING

6. **GOALS & NEEDS** (3-4 bullet points):
   - What they're trying to achieve
   - Their primary needs and desires

**CITATION FORMAT:**
For each behavioral observation, include: "Quote from their content" — [URL]

**TONE:** Professional, analytical, based on evidence. Avoid speculation without basis.
"""

    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        return f"Error generating persona: {e}"

def create_visualizations(user_data, username):
    """Create data visualizations for the user"""
    try:
        os.makedirs(f"output/{username}_analysis", exist_ok=True)

        # Set style
        plt.style.use('seaborn-v0_8')

        # 1. Subreddit Activity Chart
        if user_data['subreddit_activity']:
            fig, ax = plt.subplots(figsize=(12, 6))
            subreddits = [item[0] for item in user_data['subreddit_activity'][:10]]
            counts = [item[1] for item in user_data['subreddit_activity'][:10]]

            bars = ax.barh(subreddits, counts, color='#FF4500')
            ax.set_xlabel('Number of Posts/Comments')
            ax.set_title(f'{username} - Top Subreddit Activity')
            ax.grid(axis='x', alpha=0.3)

            # Add value labels
            for bar in bars:
                width = bar.get_width()
                ax.text(width, bar.get_y() + bar.get_height()/2,
                       f'{int(width)}', ha='left', va='center')

            plt.tight_layout()
            plt.savefig(f"output/{username}_analysis/subreddit_activity.png", dpi=300, bbox_inches='tight')
            plt.close()

        # 2. Posting Time Heatmap
        if user_data['posting_patterns']['hours']:
            fig, ax = plt.subplots(figsize=(12, 3))
            hours = list(range(24))
            activity = [user_data['posting_patterns']['hours'].get(h, 0) for h in hours]

            # Create heatmap data
            heatmap_data = [activity]

            sns.heatmap(heatmap_data,
                       xticklabels=[f"{h:02d}:00" for h in hours],
                       yticklabels=['Activity'],
                       cmap='Reds',
                       annot=False,
                       cbar_kws={'label': 'Posts/Comments'},
                       ax=ax)

            ax.set_title(f'{username} - Posting Activity by Hour (24h format)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"output/{username}_analysis/posting_times.png", dpi=300, bbox_inches='tight')
            plt.close()

        # 3. Word Cloud
        if user_data['text_analysis']['comments']['word_frequency']:
            # Combine all text
            all_text = []
            for comment in user_data['comments'][:50]:
                if comment['body'] and len(comment['body']) > 10:
                    all_text.append(comment['body'])

            if all_text:
                combined_text = ' '.join(all_text)
                # Remove common Reddit words
                stop_words = {'reddit', 'post', 'comment', 'like', 'think', 'know', 'get', 'would', 'could', 'should', 'really', 'much', 'also', 'even', 'still', 'way', 'make', 'take', 'come', 'go', 'see', 'look', 'want', 'need', 'say', 'said', 'tell', 'ask', 'give', 'find', 'use', 'work', 'try', 'feel', 'seem', 'become', 'leave', 'put', 'mean', 'keep', 'let', 'begin', 'seem', 'help', 'talk', 'turn', 'start', 'might', 'show', 'hear', 'play', 'run', 'move', 'live', 'believe', 'bring', 'happen', 'write', 'provide', 'sit', 'stand', 'lose', 'pay', 'meet', 'include', 'continue', 'set', 'learn', 'change', 'lead', 'understand', 'watch', 'follow', 'stop', 'create', 'speak', 'read', 'allow', 'add', 'spend', 'grow', 'open', 'walk', 'win', 'offer', 'remember', 'love', 'consider', 'appear', 'buy', 'wait', 'serve', 'die', 'send', 'expect', 'build', 'stay', 'fall', 'cut', 'reach', 'kill', 'remain'}

                wordcloud = WordCloud(width=800, height=400,
                                    background_color='white',
                                    stopwords=stop_words,
                                    max_words=100,
                                    colormap='viridis').generate(combined_text)

                plt.figure(figsize=(12, 6))
                plt.imshow(wordcloud, interpolation='bilinear')
                plt.axis('off')
                plt.title(f'{username} - Most Common Words', fontsize=16, pad=20)
                plt.tight_layout()
                plt.savefig(f"output/{username}_analysis/wordcloud.png", dpi=300, bbox_inches='tight')
                plt.close()

        print(f"📊 Visualizations saved to output/{username}_analysis/")

    except Exception as e:
        print(f"⚠️ Error creating visualizations: {e}")

def generate_dynamic_html_persona(username, user_data, persona_text):
    """Generate a unique, dynamic HTML persona card based on user's actual data"""
    try:
        # Parse persona text for structured data
        parsed_data = parse_persona_text(persona_text)

        # Analyze user's data for dynamic styling and content
        user_analysis = analyze_user_for_styling(user_data)

        # Generate completely dynamic HTML
        html_content = create_dynamic_html(username, user_data, parsed_data, user_analysis)

        # Save HTML file
        html_path = f"output/{username}_persona.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"🎨 Dynamic persona card saved: {html_path}")
        return html_path

    except Exception as e:
        print(f"⚠️ Error generating dynamic HTML persona: {e}")
        return None

def parse_persona_text(persona_text):
    """Parse the AI-generated persona text to extract structured data"""
    data = {
        'basic_info': {},
        'behaviors': [],
        'frustrations': [],
        'motivations': {},
        'personality': {},
        'goals': [],
        'quotes': []
    }

    lines = persona_text.split('\n')
    current_section = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Detect sections
        if 'BASIC INFO' in line.upper() or 'DEMOGRAPHICS' in line.upper():
            current_section = 'basic_info'
        elif 'BEHAVIOR' in line.upper() and 'HABIT' in line.upper():
            current_section = 'behaviors'
        elif 'FRUSTRATION' in line.upper():
            current_section = 'frustrations'
        elif 'MOTIVATION' in line.upper():
            current_section = 'motivations'
        elif 'PERSONALITY' in line.upper():
            current_section = 'personality'
        elif 'GOAL' in line.upper() and 'NEED' in line.upper():
            current_section = 'goals'

        # Extract data based on current section
        if current_section == 'basic_info':
            for field in ['age', 'occupation', 'status', 'location', 'archetype']:
                if field.upper() in line.upper() and ':' in line:
                    data['basic_info'][field] = line.split(':', 1)[1].strip()

        elif current_section in ['behaviors', 'frustrations', 'goals']:
            if line.startswith('- ') or line.startswith('• '):
                # Extract quote if present
                if '"' in line and '—' in line:
                    parts = line.split('—')
                    behavior = parts[0].strip()
                    citation = parts[1].strip() if len(parts) > 1 else ''
                    data[current_section].append({'text': behavior, 'citation': citation})
                else:
                    data[current_section].append({'text': line[2:], 'citation': ''})

        elif current_section == 'motivations':
            for motivation in ['CONVENIENCE', 'WELLNESS', 'SPEED', 'PREFERENCES', 'COMFORT']:
                if motivation in line.upper() and '/10' in line:
                    try:
                        score = int(line.split('/10')[0].split()[-1])
                        data['motivations'][motivation.lower()] = score
                    except:
                        data['motivations'][motivation.lower()] = 5

    return data

def analyze_user_for_styling(user_data):
    """Analyze user data to determine dynamic styling and layout"""
    analysis = {
        'primary_color': '#FF6B6B',  # Default
        'secondary_color': '#4ECDC4',
        'personality_type': 'balanced',
        'activity_level': 'moderate',
        'dominant_themes': [],
        'visual_style': 'modern'
    }

    # Determine color scheme based on top subreddits
    top_subs = [sub[0].lower() for sub in user_data['subreddit_activity'][:3]]

    if any(sub in ['gaming', 'games', 'pcgaming'] for sub in top_subs):
        analysis['primary_color'] = '#9B59B6'
        analysis['secondary_color'] = '#3498DB'
        analysis['visual_style'] = 'gaming'
    elif any(sub in ['art', 'design', 'photography'] for sub in top_subs):
        analysis['primary_color'] = '#E74C3C'
        analysis['secondary_color'] = '#F39C12'
        analysis['visual_style'] = 'creative'
    elif any(sub in ['science', 'technology', 'programming'] for sub in top_subs):
        analysis['primary_color'] = '#2ECC71'
        analysis['secondary_color'] = '#1ABC9C'
        analysis['visual_style'] = 'tech'
    elif any(sub in ['fitness', 'health', 'nutrition'] for sub in top_subs):
        analysis['primary_color'] = '#E67E22'
        analysis['secondary_color'] = '#D35400'
        analysis['visual_style'] = 'wellness'

    # Determine activity level
    total_activity = len(user_data['posts']) + len(user_data['comments'])
    if total_activity > 100:
        analysis['activity_level'] = 'high'
    elif total_activity < 20:
        analysis['activity_level'] = 'low'

    # Extract dominant themes from word frequency
    if user_data['text_analysis']['comments']['word_frequency']:
        analysis['dominant_themes'] = [word[0] for word in user_data['text_analysis']['comments']['word_frequency'][:5]]

    return analysis

def generate_dynamic_css(styling):
    """Generate CSS based on user's style analysis"""
    return f"""
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, {styling['primary_color']}22, {styling['secondary_color']}22);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}

        .persona-container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-areas:
                "header header"
                "content insights"
                "footer footer";
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto 1fr auto;
            min-height: 90vh;
        }}

        .persona-header {{
            grid-area: header;
            background: linear-gradient(45deg, {styling['primary_color']}, {styling['secondary_color']});
            color: white;
            padding: 40px;
            display: flex;
            align-items: center;
            gap: 30px;
        }}

        .user-avatar {{
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            border: 4px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }}

        .user-avatar[data-style="gaming"] {{
            background: linear-gradient(45deg, #9B59B6, #3498DB);
            box-shadow: 0 0 30px rgba(155, 89, 182, 0.5);
        }}

        .user-avatar[data-style="creative"] {{
            background: linear-gradient(45deg, #E74C3C, #F39C12);
            box-shadow: 0 0 30px rgba(231, 76, 60, 0.5);
        }}

        .user-avatar[data-style="tech"] {{
            background: linear-gradient(45deg, #2ECC71, #1ABC9C);
            box-shadow: 0 0 30px rgba(46, 204, 113, 0.5);
        }}

        .user-title h1 {{
            font-size: 36px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}

        .archetype {{
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }}

        .stats-bar {{
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }}

        .stat-item {{
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            text-align: center;
        }}

        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            display: block;
        }}

        .stat-label {{
            font-size: 12px;
            opacity: 0.8;
        }}

        .persona-content {{
            grid-area: content;
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-content: start;
        }}

        .section {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid {styling['primary_color']};
        }}

        .section h2 {{
            color: {styling['primary_color']};
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}

        .section-icon {{
            width: 24px;
            height: 24px;
            background: {styling['primary_color']};
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }}

        .behavior-list, .frustration-list, .goals-list {{
            list-style: none;
        }}

        .behavior-list li, .frustration-list li, .goals-list li {{
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 3px solid {styling['secondary_color']};
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }}

        .behavior-list li:hover, .frustration-list li:hover, .goals-list li:hover {{
            transform: translateX(5px);
        }}

        .motivations {{
            grid-column: span 2;
        }}

        .motivation-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}

        .motivation-item {{
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}

        .motivation-label {{
            font-size: 14px;
            font-weight: bold;
            color: #666;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        .progress-circle {{
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient({styling['primary_color']} var(--progress), #e9ecef var(--progress));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
        }}

        .progress-circle::before {{
            content: '';
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }}

        .progress-text {{
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: {styling['primary_color']};
        }}

        .data-insights {{
            grid-area: insights;
            background: #f8f9fa;
            padding: 30px;
            border-left: 1px solid #e9ecef;
        }}

        .data-insights h3 {{
            color: {styling['primary_color']};
            margin-bottom: 20px;
            font-size: 18px;
        }}

        .insight-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }}

        .persona-footer {{
            grid-area: footer;
            background: #2c3e50;
            color: white;
            padding: 20px 40px;
            text-align: center;
            font-size: 14px;
        }}

        @media (max-width: 1024px) {{
            .persona-container {{
                grid-template-areas:
                    "header"
                    "content"
                    "insights"
                    "footer";
                grid-template-columns: 1fr;
            }}

            .persona-content {{
                grid-template-columns: 1fr;
            }}

            .motivations {{
                grid-column: span 1;
            }}
        }}

        .fade-in {{
            animation: fadeIn 0.8s ease-in;
        }}

        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(20px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
    """

def generate_dynamic_avatar(username, styling):
    """Generate a unique avatar based on username and style"""
    initial = username[0].upper() if username else 'U'

    # Add style-specific elements
    if styling['visual_style'] == 'gaming':
        return f'🎮'
    elif styling['visual_style'] == 'creative':
        return f'🎨'
    elif styling['visual_style'] == 'tech':
        return f'💻'
    elif styling['visual_style'] == 'wellness':
        return f'🏃'
    else:
        return initial

def generate_stats_bar(user_data):
    """Generate statistics bar for header"""
    return f"""
        <div class="stat-item">
            <span class="stat-number">{len(user_data['posts'])}</span>
            <span class="stat-label">Posts</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{len(user_data['comments'])}</span>
            <span class="stat-label">Comments</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{user_data['user_info']['comment_karma']:,}</span>
            <span class="stat-label">Karma</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{len(user_data['subreddit_activity'])}</span>
            <span class="stat-label">Communities</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{int(user_data['user_info']['account_age_days'])}</span>
            <span class="stat-label">Days Old</span>
        </div>
    """

def generate_dynamic_layout(parsed_data, user_data):
    """Generate dynamic layout sections based on available data"""
    sections = []

    # Basic Info Section
    basic_info = parsed_data['basic_info']
    if basic_info:
        info_html = ""
        for key, value in basic_info.items():
            if value:
                info_html += f"<div class='info-item'><strong>{key.title()}:</strong> {value}</div>"

        sections.append(f"""
            <div class="section fade-in">
                <h2><span class="section-icon">👤</span>Basic Information</h2>
                {info_html}
            </div>
        """)

    # Behaviors Section
    if parsed_data['behaviors']:
        behavior_html = "<ul class='behavior-list'>"
        for behavior in parsed_data['behaviors'][:5]:
            citation = f"<small class='citation'>{behavior['citation']}</small>" if behavior['citation'] else ""
            behavior_html += f"<li>{behavior['text']} {citation}</li>"
        behavior_html += "</ul>"

        sections.append(f"""
            <div class="section fade-in">
                <h2><span class="section-icon">🎯</span>Behaviors & Habits</h2>
                {behavior_html}
            </div>
        """)

    # Frustrations Section
    if parsed_data['frustrations']:
        frustration_html = "<ul class='frustration-list'>"
        for frustration in parsed_data['frustrations'][:4]:
            frustration_html += f"<li>{frustration['text']}</li>"
        frustration_html += "</ul>"

        sections.append(f"""
            <div class="section fade-in">
                <h2><span class="section-icon">😤</span>Frustrations</h2>
                {frustration_html}
            </div>
        """)

    # Goals Section
    if parsed_data['goals']:
        goals_html = "<ul class='goals-list'>"
        for goal in parsed_data['goals'][:4]:
            goals_html += f"<li>{goal['text']}</li>"
        goals_html += "</ul>"

        sections.append(f"""
            <div class="section fade-in">
                <h2><span class="section-icon">🎯</span>Goals & Needs</h2>
                {goals_html}
            </div>
        """)

    # Motivations Section (always include with calculated values)
    motivations = calculate_motivations(user_data)
    motivation_html = "<div class='motivation-grid'>"
    for label, score in motivations.items():
        progress_percent = (score / 10) * 360  # Convert to degrees for conic-gradient
        motivation_html += f"""
            <div class="motivation-item">
                <div class="motivation-label">{label.title()}</div>
                <div class="progress-circle" style="--progress: {progress_percent}deg">
                    <div class="progress-text">{score}/10</div>
                </div>
            </div>
        """
    motivation_html += "</div>"

    sections.append(f"""
        <div class="section motivations fade-in">
            <h2><span class="section-icon">⚡</span>Motivations</h2>
            {motivation_html}
        </div>
    """)

    return "".join(sections)

def calculate_motivations(user_data):
    """Calculate motivation scores based on user data"""
    motivations = {}

    # Convenience - based on posting frequency and quick responses
    avg_words = user_data['text_analysis']['comments'].get('avg_words_per_post', 50)
    convenience_score = min(10, max(1, 10 - int(avg_words / 20)))
    motivations['convenience'] = convenience_score

    # Wellness - based on health-related subreddits
    health_subs = [s for s in user_data['subreddit_activity'] if any(term in s[0].lower() for term in ['health', 'fitness', 'wellness', 'nutrition', 'mental'])]
    wellness_score = min(10, max(1, len(health_subs) * 2 + 3))
    motivations['wellness'] = wellness_score

    # Speed - based on response time patterns and short posts
    speed_score = min(10, max(1, convenience_score + 2))
    motivations['speed'] = speed_score

    # Preferences - based on subreddit diversity
    preferences_score = min(10, max(1, len(user_data['subreddit_activity'])))
    motivations['preferences'] = preferences_score

    # Comfort - based on consistent posting patterns
    posting_variance = len(set(user_data['posting_patterns']['hours'].values())) if user_data['posting_patterns']['hours'] else 5
    comfort_score = min(10, max(1, 10 - posting_variance))
    motivations['comfort'] = comfort_score

    return motivations

def generate_inline_visualizations(user_data):
    """Generate inline data visualizations"""
    viz_html = ""

    # Top Subreddits
    if user_data['subreddit_activity']:
        subreddit_html = "<div class='insight-card'><h4>Top Communities</h4><ul>"
        for sub, count in user_data['subreddit_activity'][:5]:
            percentage = (count / sum(s[1] for s in user_data['subreddit_activity'][:10])) * 100
            subreddit_html += f"<li>r/{sub} <span style='float: right'>{count} ({percentage:.1f}%)</span></li>"
        subreddit_html += "</ul></div>"
        viz_html += subreddit_html

    # Activity Pattern
    if user_data['posting_patterns']['hours']:
        most_active = user_data['posting_patterns']['most_active_hour']
        activity_html = f"""
            <div class='insight-card'>
                <h4>Activity Pattern</h4>
                <p>Most active at <strong>{most_active}:00</strong></p>
                <p>Total posts: <strong>{len(user_data['posts'])}</strong></p>
                <p>Total comments: <strong>{len(user_data['comments'])}</strong></p>
            </div>
        """
        viz_html += activity_html

    # Sentiment Analysis
    sentiment = user_data['text_analysis']['comments']['sentiment']
    sentiment_html = f"""
        <div class='insight-card'>
            <h4>Communication Style</h4>
            <p>Positive: <strong>{sentiment['positive']:.1%}</strong></p>
            <p>Neutral: <strong>{sentiment['neutral']:.1%}</strong></p>
            <p>Negative: <strong>{sentiment['negative']:.1%}</strong></p>
        </div>
    """
    viz_html += sentiment_html

    # Word Frequency
    if user_data['text_analysis']['comments']['word_frequency']:
        words_html = "<div class='insight-card'><h4>Common Words</h4><ul>"
        for word, freq in user_data['text_analysis']['comments']['word_frequency'][:5]:
            words_html += f"<li>{word} <span style='float: right'>{freq}</span></li>"
        words_html += "</ul></div>"
        viz_html += words_html

    return viz_html

def generate_dynamic_javascript(user_data):
    """Generate JavaScript for interactive elements"""
    return f"""
        // Add fade-in animation on load
        document.addEventListener('DOMContentLoaded', function() {{
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {{
                setTimeout(() => {{
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }}, index * 100);
            }});
        }});

        // Add hover effects for interactive elements
        document.querySelectorAll('.behavior-list li, .frustration-list li, .goals-list li').forEach(item => {{
            item.addEventListener('mouseenter', function() {{
                this.style.backgroundColor = '#e3f2fd';
            }});
            item.addEventListener('mouseleave', function() {{
                this.style.backgroundColor = 'white';
            }});
        }});

        // Add click to copy functionality for citations
        document.querySelectorAll('.citation').forEach(citation => {{
            citation.style.cursor = 'pointer';
            citation.title = 'Click to copy URL';
            citation.addEventListener('click', function() {{
                navigator.clipboard.writeText(this.textContent);
                this.style.color = '#4CAF50';
                setTimeout(() => {{
                    this.style.color = '';
                }}, 1000);
            }});
        }});
    """

def create_dynamic_html(username, user_data, parsed_data, styling):
    """Create completely dynamic HTML based on user's unique data"""

    # Generate all components
    css_styles = generate_dynamic_css(styling)
    layout_sections = generate_dynamic_layout(parsed_data, user_data)
    data_visualizations = generate_inline_visualizations(user_data)
    avatar = generate_dynamic_avatar(username, styling)
    stats_bar = generate_stats_bar(user_data)
    javascript = generate_dynamic_javascript(user_data)

    # Get archetype from parsed data or generate one
    archetype = parsed_data['basic_info'].get('archetype', f"Active {styling['visual_style'].title()} User")

    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{username} - Dynamic User Persona</title>
    <style>
        {css_styles}
    </style>
</head>
<body>
    <div class="persona-container">
        <header class="persona-header">
            <div class="user-avatar" data-style="{styling['visual_style']}">
                {avatar}
            </div>
            <div class="user-title">
                <h1>{username}</h1>
                <p class="archetype">{archetype}</p>
                <div class="stats-bar">
                    {stats_bar}
                </div>
            </div>
        </header>

        <main class="persona-content">
            {layout_sections}
        </main>

        <aside class="data-insights">
            <h3>📊 Data Insights</h3>
            {data_visualizations}
        </aside>

        <footer class="persona-footer">
            <p>Generated on {datetime.now().strftime('%B %d, %Y')} | Based on {len(user_data['posts'])} posts and {len(user_data['comments'])} comments | Unique persona for {username}</p>
        </footer>
    </div>

    <script>
        {javascript}
    </script>
</body>
</html>"""

    return html

def save_comprehensive_report(username, user_data, persona_text):
    """Save comprehensive report with all data"""
    os.makedirs("output", exist_ok=True)

    # Save persona
    persona_path = f"output/{username}_persona.txt"
    with open(persona_path, "w", encoding="utf-8") as f:
        f.write(f"# USER PERSONA: {username.upper()}\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("=" * 80 + "\n\n")
        f.write(persona_text)
        f.write("\n\n" + "=" * 80 + "\n")
        f.write("## DATA ANALYSIS SUMMARY\n\n")

        # Add data summary
        f.write(f"**Account Statistics:**\n")
        f.write(f"- Username: {user_data['user_info']['username']}\n")
        f.write(f"- Account Age: {user_data['user_info']['account_age_days']:.0f} days\n")
        f.write(f"- Comment Karma: {user_data['user_info']['comment_karma']:,}\n")
        f.write(f"- Link Karma: {user_data['user_info']['link_karma']:,}\n")
        f.write(f"- Posts Analyzed: {len(user_data['posts'])}\n")
        f.write(f"- Comments Analyzed: {len(user_data['comments'])}\n\n")

        f.write(f"**Top Subreddits:**\n")
        for sub, count in user_data['subreddit_activity'][:10]:
            f.write(f"- r/{sub}: {count} posts/comments\n")

        f.write(f"\n**Behavioral Patterns:**\n")
        f.write(f"- Most Active Hour: {user_data['posting_patterns']['most_active_hour']}:00\n")
        f.write(f"- Average Sentiment: {user_data['text_analysis']['comments']['sentiment']['compound']:.2f}\n")
        f.write(f"- Writing Complexity: {user_data['text_analysis']['comments']['reading_ease']:.0f} (Flesch Reading Ease)\n")

        f.write(f"\n**Citations and Sources:**\n")
        f.write("All persona traits are derived from the user's actual Reddit posts and comments.\n")
        f.write("URLs are provided for verification and deeper analysis.\n")

    # Save raw data as JSON
    json_path = f"output/{username}_raw_data.json"
    with open(json_path, "w", encoding="utf-8") as f:
        # Convert datetime objects to strings for JSON serialization
        json_data = user_data.copy()
        for post in json_data['posts']:
            post['created_utc'] = datetime.fromtimestamp(post['created_utc']).isoformat()
        for comment in json_data['comments']:
            comment['created_utc'] = datetime.fromtimestamp(comment['created_utc']).isoformat()
        json_data['user_info']['created_utc'] = datetime.fromtimestamp(json_data['user_info']['created_utc']).isoformat()

        json.dump(json_data, f, indent=2, ensure_ascii=False)

    print(f"\n✅ Comprehensive report saved:")
    print(f"   📄 Persona: {persona_path}")
    print(f"   📊 Raw Data: {json_path}")

def main():
    print("🎯 REDDIT USER PERSONA GENERATOR")
    print("=" * 50)

    profile_url = input("Paste Reddit profile URL: ").strip()
    username = extract_username(profile_url)

    if not username:
        print("❌ Invalid Reddit profile URL.")
        print("Expected format: https://www.reddit.com/user/username/")
        return

    print(f"\n🔎 Starting comprehensive analysis for: {username}")
    print("This may take a few minutes...")

    # Scrape and analyze data
    user_data = scrape_user_data(username, limit=150)

    if not user_data:
        print("❌ Failed to scrape user data. Check username and try again.")
        return

    print(f"✅ Data collection complete!")
    print(f"   📝 Posts: {len(user_data['posts'])}")
    print(f"   💬 Comments: {len(user_data['comments'])}")
    print(f"   🏷️ Subreddits: {len(user_data['subreddit_activity'])}")

    # Generate persona
    print(f"\n🤖 Generating professional persona...")
    persona_text = generate_professional_persona(user_data)

    # Create visualizations
    print(f"📊 Creating data visualizations...")
    create_visualizations(user_data, username)

    # Generate HTML persona card
    print(f"🎨 Creating visual persona card...")
    html_path = generate_dynamic_html_persona(username, user_data, persona_text)

    # Save everything
    save_comprehensive_report(username, user_data, persona_text)

    print(f"\n🎉 Analysis complete for {username}!")
    print(f"📁 Generated files:")
    print(f"   📄 Text Persona: output/{username}_persona.txt")
    print(f"   🎨 Visual Card: output/{username}_persona.html")
    print(f"   📊 Charts: output/{username}_analysis/")
    print(f"   📋 Raw Data: output/{username}_raw_data.json")

    if html_path:
        print(f"\n💡 Open {html_path} in your browser to see the visual persona card!")

if __name__ == "__main__":
    main()
