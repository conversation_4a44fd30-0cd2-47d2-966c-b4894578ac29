#!/usr/bin/env python3
"""
Setup script for Reddit User Persona Generator
This script helps you set up the environment and dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def create_env_file():
    """Create .env file template"""
    env_content = """# Reddit API Credentials
# Get these from: https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_client_id_here
REDDIT_CLIENT_SECRET=your_client_secret_here
REDDIT_USER_AGENT=PersonaGenerator/1.0 by YourUsername

# Google Gemini API Key
# Get this from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
"""
    
    env_path = Path(".env")
    if not env_path.exists():
        with open(env_path, "w") as f:
            f.write(env_content)
        print("✅ Created .env file template")
        print("📝 Please edit .env file with your API credentials")
        return True
    else:
        print("ℹ️ .env file already exists")
        return True

def create_output_directory():
    """Create output directory"""
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    print("✅ Created output directory")

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def main():
    print("🚀 Reddit User Persona Generator Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install requirements
    if not install_requirements():
        return
    
    # Create .env file
    create_env_file()
    
    # Create output directory
    create_output_directory()
    
    print("\n🎉 Setup complete!")
    print("\n📋 Next steps:")
    print("1. Edit the .env file with your API credentials:")
    print("   - Reddit API: https://www.reddit.com/prefs/apps")
    print("   - Gemini API: https://makersuite.google.com/app/apikey")
    print("2. Run: python personal.py")
    print("\n💡 Need help? Check the README for detailed instructions.")

if __name__ == "__main__":
    main()
